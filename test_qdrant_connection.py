#!/usr/bin/env python3
"""
Simple test to verify Qdrant connection and collections
"""
import os
from dotenv import load_dotenv
from qdrant_client import QdrantClient

# Load environment variables
load_dotenv()

def test_connection():
    """Test basic Qdrant connection"""
    print("🔍 Testing Qdrant Connection...")
    
    try:
        # Get configuration
        url = os.getenv("QDRANT_URL")
        api_key = os.getenv("QDRANT_API_KEY")
        
        if not url or not api_key:
            print("❌ Missing QDRANT_URL or QDRANT_API_KEY in .env file")
            return False
        
        print(f"📡 Connecting to: {url}")
        
        # Initialize client with minimal parameters
        client = QdrantClient(
            url=url,
            api_key=api_key,
            timeout=60
        )
        
        # Test connection by getting collections
        print("📋 Getting collections...")
        collections = client.get_collections()
        
        print(f"✅ Connected successfully!")
        print(f"📊 Found {len(collections.collections)} collections:")
        
        # Check for our specific collections
        collection_names = [col.name for col in collections.collections]
        
        for name in collection_names:
            print(f"   📁 {name}")
        
        # Check for TAX-RAG-1
        if "TAX-RAG-1" in collection_names:
            print("✅ TAX-RAG-1 collection found")
            try:
                info = client.get_collection("TAX-RAG-1")
                print(f"   📏 Vector size: {info.config.params.vectors.size}")
                print(f"   📊 Points count: {info.points_count}")
                print(f"   📐 Distance: {info.config.params.vectors.distance}")
            except Exception as e:
                print(f"   ⚠️  Error getting TAX-RAG-1 info: {e}")
        else:
            print("❌ TAX-RAG-1 collection NOT found")
        
        # Check for tax_documents
        if "tax_documents" in collection_names:
            print("✅ tax_documents collection found")
            try:
                info = client.get_collection("tax_documents")
                print(f"   📏 Vector size: {info.config.params.vectors.size}")
                print(f"   📊 Points count: {info.points_count}")
                print(f"   📐 Distance: {info.config.params.vectors.distance}")
            except Exception as e:
                print(f"   ⚠️  Error getting tax_documents info: {e}")
        else:
            print("❌ tax_documents collection NOT found")
        
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        print("\n🔧 Troubleshooting tips:")
        print("1. Check your QDRANT_URL format (should include https://)")
        print("2. Verify your QDRANT_API_KEY is correct")
        print("3. Ensure your Qdrant cluster is running")
        print("4. Check your internet connection")
        return False

if __name__ == "__main__":
    test_connection()