"""
Flask web application for Multimodal RAG Chatbot
"""
import os
import asyncio
import uuid
from datetime import datetime
from flask import Flask, render_template, request, jsonify, session
from flask_cors import CORS
from werkzeug.utils import secure_filename

from config import Config
from src.chatbot_agent import MultimodalRAGAgent

# Initialize Flask app
app = Flask(__name__)
app.secret_key = os.urandom(24)
CORS(app)

# Configure upload settings
UPLOAD_FOLDER = 'uploads'
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = Config.MAX_FILE_SIZE_MB * 1024 * 1024

# Initialize chatbot agent
chatbot_agent = None

def get_event_loop():
    """Get or create event loop for async operations"""
    try:
        loop = asyncio.get_running_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    return loop

def initialize_chatbot():
    """Initialize the chatbot agent"""
    global chatbot_agent
    if chatbot_agent is None:
        try:
            chatbot_agent = MultimodalRAGAgent()
            loop = get_event_loop()
            loop.run_until_complete(chatbot_agent.initialize())
            print("✅ Chatbot agent initialized successfully")
        except Exception as e:
            print(f"❌ Failed to initialize chatbot: {e}")
            chatbot_agent = None

@app.route('/')
def index():
    """Main chat interface"""
    if 'session_id' not in session:
        session['session_id'] = str(uuid.uuid4())
    return render_template('index.html')

@app.route('/chat', methods=['POST'])
def chat():
    """Handle chat messages"""
    if not chatbot_agent:
        return jsonify({
            'error': 'Chatbot not initialized. Please check your configuration.',
            'response': 'Sorry, the chatbot is not available right now.'
        }), 500
    
    try:
        data = request.get_json()
        message = data.get('message', '').strip()
        
        if not message:
            return jsonify({'error': 'Message cannot be empty'}), 400
        
        session_id = session.get('session_id', str(uuid.uuid4()))
        
        # Process the query
        loop = get_event_loop()
        result = loop.run_until_complete(
            chatbot_agent.process_query(
                query=message,
                session_id=session_id
            )
        )
        
        return jsonify({
            'response': result['answer'],
            'sources': result.get('sources', []),
            'route': result.get('route', 'unknown'),
            'session_id': session_id,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        print(f"Error in chat endpoint: {e}")
        return jsonify({
            'error': str(e),
            'response': 'Sorry, I encountered an error processing your request.'
        }), 500

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file uploads and document analysis"""
    if not chatbot_agent:
        return jsonify({'error': 'Chatbot not initialized'}), 500
    
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file uploaded'}), 400
        
        file = request.files['file']
        message = request.form.get('message', '').strip()
        
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if not message:
            return jsonify({'error': 'Please provide a question about the document'}), 400
        
        # Check file type
        filename = secure_filename(file.filename)
        file_extension = filename.split('.')[-1].lower()
        
        if file_extension not in Config.SUPPORTED_FILE_TYPES:
            return jsonify({
                'error': f'Unsupported file type. Supported: {", ".join(Config.SUPPORTED_FILE_TYPES)}'
            }), 400
        
        # Read file content
        file_content = file.read()
        
        # Check file size
        if len(file_content) > Config.MAX_FILE_SIZE_MB * 1024 * 1024:
            return jsonify({
                'error': f'File size exceeds {Config.MAX_FILE_SIZE_MB}MB limit'
            }), 400
        
        session_id = session.get('session_id', str(uuid.uuid4()))
        
        # Process document query
        loop = get_event_loop()
        result = loop.run_until_complete(
            chatbot_agent.process_document_query(
                query=message,
                file_content=file_content,
                filename=filename,
                session_id=session_id
            )
        )
        
        return jsonify({
            'response': result['answer'],
            'sources': result.get('sources', []),
            'route': 'document_analysis',
            'filename': filename,
            'session_id': session_id,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        print(f"Error in upload endpoint: {e}")
        return jsonify({
            'error': str(e),
            'response': 'Sorry, I encountered an error processing your document.'
        }), 500

@app.route('/history')
def get_history():
    """Get conversation history"""
    if not chatbot_agent:
        return jsonify({'error': 'Chatbot not initialized'}), 500
    
    try:
        session_id = session.get('session_id')
        if not session_id:
            return jsonify({'history': []})
        
        loop = get_event_loop()
        history = loop.run_until_complete(
            chatbot_agent.get_conversation_history(session_id)
        )
        
        return jsonify({'history': history})
        
    except Exception as e:
        print(f"Error getting history: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/clear', methods=['POST'])
def clear_history():
    """Clear conversation history"""
    if not chatbot_agent:
        return jsonify({'error': 'Chatbot not initialized'}), 500
    
    try:
        session_id = session.get('session_id')
        if session_id:
            loop = get_event_loop()
            loop.run_until_complete(chatbot_agent.clear_session(session_id))
        
        return jsonify({'message': 'History cleared successfully'})
        
    except Exception as e:
        print(f"Error clearing history: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/health')
def health_check():
    """Health check endpoint"""
    status = {
        'status': 'healthy' if chatbot_agent else 'unhealthy',
        'chatbot_initialized': chatbot_agent is not None,
        'timestamp': datetime.now().isoformat()
    }
    
    return jsonify(status)

@app.errorhandler(413)
def too_large(e):
    return jsonify({'error': f'File too large. Maximum size: {Config.MAX_FILE_SIZE_MB}MB'}), 413

@app.errorhandler(500)
def internal_error(e):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    print("🚀 Starting Multimodal RAG Chatbot")
    print("=" * 50)
    
    try:
        # Validate configuration
        Config.validate_config()
        print("✅ Configuration validated")

        # Initialize chatbot agent
        print("🤖 Initializing chatbot agent...")
        initialize_chatbot()

        # Start Flask app
        print("🌐 Starting Flask server...")
        print(f"📱 Access the chatbot at: http://localhost:5000")
        
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=Config.DEBUG
        )
        
    except Exception as e:
        print(f"❌ Failed to start application: {e}")
        print("💡 Please check your .env configuration")