#!/usr/bin/env python3
"""
Quick fix script to test and resolve the Qdrant connection issue
"""
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_basic_connection():
    """Test basic Qdrant connection"""
    print("🔧 Quick Fix: Testing Qdrant Connection")
    print("=" * 50)
    
    # Check environment variables
    url = os.getenv("QDRANT_URL")
    api_key = os.getenv("QDRANT_API_KEY")
    
    if not url:
        print("❌ QDRANT_URL not found in .env file")
        return False
    
    if not api_key:
        print("❌ QDRANT_API_KEY not found in .env file")
        return False
    
    print(f"📡 URL: {url}")
    print(f"🔑 API Key: {api_key[:10]}...")
    
    try:
        # Test with different client versions
        print("\n1. Testing Qdrant client connection...")
        
        from qdrant_client import QdrantClient
        
        # Try basic connection
        client = QdrantClient(
            url=url,
            api_key=api_key,
            timeout=60
        )
        
        print("2. Getting collections...")
        collections = client.get_collections()
        
        print(f"✅ Success! Found {len(collections.collections)} collections")
        
        # List all collections
        collection_names = [col.name for col in collections.collections]
        for name in collection_names:
            print(f"   📁 {name}")
        
        # Check our specific collections
        print("\n3. Checking required collections...")
        
        if "TAX-RAG-1" in collection_names:
            print("✅ TAX-RAG-1 found")
            try:
                info = client.get_collection("TAX-RAG-1")
                print(f"   📊 Points: {info.points_count}")
                print(f"   📏 Vector size: {info.config.params.vectors.size}")
            except Exception as e:
                print(f"   ⚠️  Error getting info: {e}")
        else:
            print("❌ TAX-RAG-1 NOT found")
        
        if "tax_documents" in collection_names:
            print("✅ tax_documents found")
            try:
                info = client.get_collection("tax_documents")
                print(f"   📊 Points: {info.points_count}")
                print(f"   📏 Vector size: {info.config.params.vectors.size}")
            except Exception as e:
                print(f"   ⚠️  Error getting info: {e}")
        else:
            print("❌ tax_documents NOT found")
        
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        print(f"Error type: {type(e).__name__}")
        
        # Provide specific troubleshooting
        error_str = str(e).lower()
        if "proxies" in error_str:
            print("\n🔧 Fix: Update qdrant-client version")
            print("Run: pip install qdrant-client==1.6.4")
        elif "timeout" in error_str:
            print("\n🔧 Fix: Check your internet connection and Qdrant URL")
        elif "unauthorized" in error_str or "403" in error_str:
            print("\n🔧 Fix: Check your QDRANT_API_KEY")
        elif "404" in error_str:
            print("\n🔧 Fix: Check your QDRANT_URL")
        
        return False

def test_openai_connection():
    """Test OpenAI connection"""
    print("\n4. Testing OpenAI connection...")
    
    openai_key = os.getenv("OPENAI_API_KEY")
    if not openai_key:
        print("❌ OPENAI_API_KEY not found")
        return False
    
    try:
        from langchain_openai import OpenAIEmbeddings
        
        embeddings = OpenAIEmbeddings(
            model="text-embedding-3-small",
            openai_api_key=openai_key
        )
        
        # Test embedding
        test_embedding = embeddings.embed_query("test")
        print(f"✅ OpenAI working! Embedding dimension: {len(test_embedding)}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI connection failed: {e}")
        return False

def main():
    """Main test function"""
    success = True
    
    # Test Qdrant
    if not test_basic_connection():
        success = False
    
    # Test OpenAI
    if not test_openai_connection():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed! You can now run:")
        print("   python verify_indexes.py")
        print("   python app.py")
    else:
        print("❌ Some tests failed. Please fix the issues above.")
    
    return success

if __name__ == "__main__":
    main()