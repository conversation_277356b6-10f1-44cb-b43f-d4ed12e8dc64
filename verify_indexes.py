#!/usr/bin/env python3
"""
Script to verify Qdrant index configurations and compatibility
"""
import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from config import Config
from src.qdrant_manager import QdrantManager

async def main():
    """Verify index configurations"""
    print("🔍 Verifying Qdrant Index Configurations")
    print("=" * 60)
    
    try:
        # Test basic connection first
        print("1. Testing basic Qdrant connection...")
        from qdrant_client import QdrantClient
        
        client = QdrantClient(
            url=Config.QDRANT_URL,
            api_key=Config.QDRANT_API_KEY,
            timeout=60
        )
        
        # Test connection
        collections = client.get_collections()
        print(f"   ✅ Connected! Found {len(collections.collections)} collections")
        
        # Initialize Qdrant manager
        print("2. Initializing Qdrant manager...")
        qdrant_manager = QdrantManager()
        await qdrant_manager.initialize()
        
        print("\n2. Collection Information:")
        print("-" * 40)
        
        # Get collection info
        collections_info = qdrant_manager.get_collections_info()
        
        for collection_name, info in collections_info.items():
            print(f"\n📁 Collection: {collection_name}")
            if info.get('exists'):
                print(f"   ✅ Status: Found")
                print(f"   📏 Vector Size: {info.get('vector_size')}")
                print(f"   📐 Distance Metric: {info.get('distance')}")
                print(f"   📊 Points Count: {info.get('points_count')}")
            else:
                print(f"   ❌ Status: Not Found")
                print(f"   💡 Please ensure the collection exists in your Qdrant instance")
        
        print("\n3. Collection Statistics:")
        print("-" * 40)
        
        stats = await qdrant_manager.get_collection_stats()
        for collection_name, stat in stats.items():
            print(f"\n📊 {collection_name}:")
            if 'error' in stat:
                print(f"   ❌ Error: {stat['error']}")
            elif stat.get('status') == 'not_found':
                print(f"   ❌ Status: Collection not found")
            else:
                print(f"   ✅ Status: {stat.get('status', 'Unknown')}")
                print(f"   📈 Points: {stat.get('points_count', 0)}")
                print(f"   📏 Vector Size: {stat.get('vector_size', 'Unknown')}")
                print(f"   📐 Distance: {stat.get('distance_metric', 'Unknown')}")
        
        print("\n4. Testing Search Functionality:")
        print("-" * 40)
        
        # Test search in both collections
        test_query = "income tax filing requirements"
        print(f"\n🔍 Testing search with query: '{test_query}'")
        
        # Search TAX-RAG-1
        tax_rag_results = await qdrant_manager.search_collection(
            Config.QDRANT_TAX_RAG_COLLECTION,
            test_query,
            limit=3
        )
        print(f"\n📁 {Config.QDRANT_TAX_RAG_COLLECTION} Results: {len(tax_rag_results)} found")
        for i, result in enumerate(tax_rag_results[:2], 1):
            print(f"   {i}. Score: {result['score']:.3f} | Text: {result['text'][:100]}...")
        
        # Search tax_documents
        tax_docs_results = await qdrant_manager.search_collection(
            Config.QDRANT_TAX_DOCUMENTS_COLLECTION,
            test_query,
            limit=3
        )
        print(f"\n📁 {Config.QDRANT_TAX_DOCUMENTS_COLLECTION} Results: {len(tax_docs_results)} found")
        for i, result in enumerate(tax_docs_results[:2], 1):
            print(f"   {i}. Score: {result['score']:.3f} | Text: {result['text'][:100]}...")
        
        print("\n5. Configuration Summary:")
        print("-" * 40)
        print(f"✅ TAX-RAG-1 Collection: {Config.QDRANT_TAX_RAG_COLLECTION}")
        print(f"✅ Tax Documents Collection: {Config.QDRANT_TAX_DOCUMENTS_COLLECTION}")
        print(f"✅ Qdrant URL: {Config.QDRANT_URL}")
        print(f"✅ OpenAI Embedding Model: {Config.OPENAI_EMBEDDING_MODEL}")
        
        # Check if both collections have data
        total_results = len(tax_rag_results) + len(tax_docs_results)
        if total_results > 0:
            print(f"\n🎉 SUCCESS: Found {total_results} total results across both collections")
            print("✅ Your indexes are properly configured and contain data!")
        else:
            print(f"\n⚠️  WARNING: No search results found in either collection")
            print("💡 Please ensure your collections contain embedded documents")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERROR: {str(e)}")
        print("\n🔧 Troubleshooting Tips:")
        print("1. Check your .env file has correct QDRANT_URL and QDRANT_API_KEY")
        print("2. Verify collection names: TAX-RAG-1 and tax_documents")
        print("3. Ensure collections exist in your Qdrant instance")
        print("4. Check if collections have vector data")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        if success:
            print("\n✅ Index verification completed successfully!")
        else:
            print("\n❌ Index verification failed!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️  Verification interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)