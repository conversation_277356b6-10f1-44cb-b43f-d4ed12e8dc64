"""
RAG Pipeline for handling retrieval-augmented generation
"""
import asyncio
from typing import List, Dict, Any, Optional
from openai import Open<PERSON><PERSON>

from src.qdrant_manager import <PERSON>drant<PERSON>anager
from src.models import RAGResult
from config import Config


class RAGPipeline:
    """
    Retrieval-Augmented Generation pipeline that combines vector search with LLM generation
    """

    def __init__(self):
        self.config = Config
        self.qdrant_manager = QdrantManager()
        self.openai_client = None
        self.initialized = False

    async def initialize(self):
        """Initialize the RAG pipeline components"""
        try:
            # Initialize Qdrant manager
            await self.qdrant_manager.initialize()

            # Initialize OpenAI client
            self.openai_client = OpenAI(
                api_key=self.config.OPENAI_API_KEY
            )

            self.initialized = True
            print("RAG Pipeline initialized successfully")

        except Exception as e:
            raise Exception(f"Failed to initialize RAG Pipeline: {str(e)}")

    async def query(self, query: str, conversation_history: List[Dict[str, str]] = None) -> RAGResult:
        """
        Process a query through the RAG pipeline

        Args:
            query: User's question
            conversation_history: Previous conversation messages

        Returns:
            RAGResult with answer, sources, context chunks, and confidence
        """
        if not self.initialized:
            raise Exception("RAG Pipeline not initialized. Call initialize() first.")

        try:
            # Step 1: Retrieve top 5 chunks from each collection (total 10 chunks)
            search_results = await self.qdrant_manager.search_both_collections(
                query=query,
                limit_per_collection=5  # Get top 5 from each collection
            )

            # Step 2: Combine all retrieved chunks for intelligent selection
            all_chunks = []

            # Process TAX-RAG-1 results
            for result in search_results.get('tax_rag', []):
                text_content = result.get('text', '')
                if not text_content:
                    # Try alternative text fields
                    text_content = result.get('payload', {}).get('text', '') or result.get('payload', {}).get('content', '')

                if text_content.strip():  # Only include non-empty chunks
                    all_chunks.append({
                        'text': text_content,
                        'score': result['score'],
                        'source_info': result['source_info'],
                        'collection': 'TAX-RAG-1'
                    })

            # Process tax_documents results
            for result in search_results.get('tax_documents', []):
                text_content = result.get('text', '')
                if not text_content:
                    # Try alternative text fields
                    text_content = result.get('payload', {}).get('text', '') or result.get('payload', {}).get('content', '')

                if text_content.strip():  # Only include non-empty chunks
                    all_chunks.append({
                        'text': text_content,
                        'score': result['score'],
                        'source_info': result['source_info'],
                        'collection': 'tax_documents'
                    })

            # Step 3: Intelligent chunk selection - rank by relevance score
            selected_chunks = self._select_best_chunks(all_chunks, query)

            # Extract context and sources from selected chunks
            context_chunks = [chunk['text'] for chunk in selected_chunks]
            source_attributions = self._format_source_attributions(selected_chunks)

            # Step 4: Generate answer with mandatory source attribution
            answer = await self._generate_answer(
                query=query,
                context_chunks=context_chunks,
                source_attributions=source_attributions,
                conversation_history=conversation_history or []
            )

            # Step 5: Calculate confidence based on retrieval scores
            confidence = self._calculate_confidence_from_chunks(selected_chunks)

            return RAGResult(
                answer=answer,
                sources=[attr['display_name'] for attr in source_attributions],
                context_chunks=context_chunks,
                confidence=confidence
            )

        except Exception as e:
            # Return error response
            return RAGResult(
                answer=f"I apologize, but I encountered an error while processing your question: {str(e)}",
                sources=[],
                context_chunks=[],
                confidence=0.0
            )

    def _select_best_chunks(self, all_chunks: List[Dict], query: str) -> List[Dict]:
        """
        Intelligent chunk selection from retrieved results with token limit consideration
        """
        if not all_chunks:
            return []

        # Sort by relevance score (higher is better)
        sorted_chunks = sorted(all_chunks, key=lambda x: x['score'], reverse=True)

        # Select chunks with token budget management
        selected_chunks = []
        used_sources = set()
        total_tokens = 0
        max_tokens = 6000  # Leave room for prompt and response

        # Estimate tokens (rough approximation: 1 token ≈ 4 characters)
        def estimate_tokens(text):
            return len(text) // 4

        # First pass: select highest scoring chunks from different sources
        for chunk in sorted_chunks:
            chunk_tokens = estimate_tokens(chunk['text'])
            source_key = chunk['source_info']['document_name']

            if (total_tokens + chunk_tokens <= max_tokens and
                len(selected_chunks) < 6 and  # Reduced from 8 to 6
                source_key not in used_sources):
                selected_chunks.append(chunk)
                used_sources.add(source_key)
                total_tokens += chunk_tokens

        # Second pass: fill remaining slots with best remaining chunks if we have token budget
        for chunk in sorted_chunks:
            if len(selected_chunks) >= 8:  # Maximum 8 chunks (reduced from 10)
                break
            if chunk not in selected_chunks:
                chunk_tokens = estimate_tokens(chunk['text'])
                if total_tokens + chunk_tokens <= max_tokens:
                    selected_chunks.append(chunk)
                    total_tokens += chunk_tokens

        return selected_chunks

    def _format_source_attributions(self, selected_chunks: List[Dict]) -> List[Dict]:
        """
        Format source attributions for display
        """
        source_map = {}

        for chunk in selected_chunks:
            source_info = chunk['source_info']
            doc_name = source_info['document_name']

            if doc_name not in source_map:
                # Create display name
                display_parts = [doc_name]
                if source_info.get('page'):
                    display_parts.append(f"Page {source_info['page']}")
                if source_info.get('section'):
                    display_parts.append(f"Section: {source_info['section']}")

                source_map[doc_name] = {
                    'document_name': doc_name,
                    'display_name': " - ".join(display_parts),
                    'collection': chunk['collection'],
                    'pages': set(),
                    'sections': set()
                }

            # Add page and section info
            if source_info.get('page'):
                source_map[doc_name]['pages'].add(str(source_info['page']))
            if source_info.get('section'):
                source_map[doc_name]['sections'].add(source_info['section'])

        # Convert sets to sorted lists for consistent display
        for source in source_map.values():
            if source['pages']:
                pages_str = ", ".join(sorted(source['pages'], key=lambda x: int(x) if x.isdigit() else 0))
                source['display_name'] = f"{source['document_name']} (Pages: {pages_str})"
            elif source['sections']:
                sections_str = ", ".join(sorted(source['sections']))
                source['display_name'] = f"{source['document_name']} ({sections_str})"

        return list(source_map.values())

    async def _generate_answer(
        self,
        query: str,
        context_chunks: List[str],
        source_attributions: List[Dict],
        conversation_history: List[Dict[str, str]]
    ) -> str:
        """Generate answer using OpenAI with retrieved context"""

        # Build context string
        context_text = "\n\n".join([f"Context {i+1}:\n{chunk}" for i, chunk in enumerate(context_chunks)])

        # Build conversation history string
        history_text = ""
        if conversation_history:
            history_text = "\n".join([
                f"{msg['role'].title()}: {msg['content']}"
                for msg in conversation_history[-4:]  # Last 4 messages for context
            ])

        # Format sources for inclusion in the prompt
        sources_text = ""
        if source_attributions:
            sources_list = [f"• {attr['display_name']}" for attr in source_attributions]
            sources_text = "\n".join(sources_list)

        # Create system prompt with mandatory source attribution requirement
        system_prompt = """You are a helpful AI assistant specializing in tax-related questions.
Use the provided context to answer the user's question accurately and comprehensively.

CRITICAL REQUIREMENTS:
- Base your answer primarily on the provided context
- ALWAYS include source citations at the end of your response
- Format sources as: "Sources: [list of document names]"
- If the context doesn't contain enough information, say so clearly but still cite the sources you used
- Provide specific details and examples when available
- Be concise but thorough
- If asked about tax calculations, show step-by-step reasoning
- Always maintain a professional and helpful tone

MANDATORY: Every response must end with proper source attribution."""

        # Create user prompt with source information
        user_prompt = f"""Context Information:
{context_text}

Available Sources:
{sources_text}

{"Previous Conversation:" + chr(10) + history_text + chr(10) if history_text else ""}
Current Question: {query}

Please provide a comprehensive answer based on the context provided.
IMPORTANT: End your response with "Sources: [list the relevant document names from the Available Sources above]"."""

        try:
            # Generate response using OpenAI
            response = await asyncio.to_thread(
                self.openai_client.chat.completions.create,
                model=self.config.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=1000,
                temperature=0.3
            )

            generated_response = response.choices[0].message.content.strip()

            # Ensure source attribution is included (fallback if LLM doesn't include it)
            if "Sources:" not in generated_response and source_attributions:
                source_names = [attr['display_name'] for attr in source_attributions]
                sources_line = f"\n\nSources: {', '.join(source_names)}"
                generated_response += sources_line

            return generated_response

        except Exception as e:
            # Even in error cases, include source attribution if available
            error_msg = f"I apologize, but I encountered an error generating the response: {str(e)}"
            if source_attributions:
                source_names = [attr['display_name'] for attr in source_attributions]
                error_msg += f"\n\nSources: {', '.join(source_names)}"
            return error_msg

    def _calculate_confidence_from_chunks(self, selected_chunks: List[Dict]) -> float:
        """Calculate confidence score from selected chunks"""
        if not selected_chunks:
            return 0.0

        # Calculate average score from selected chunks
        scores = [chunk['score'] for chunk in selected_chunks]
        avg_score = sum(scores) / len(scores)

        # Boost confidence if we have chunks from multiple collections
        collections = set(chunk['collection'] for chunk in selected_chunks)
        diversity_bonus = 0.1 if len(collections) > 1 else 0.0

        # Boost confidence based on number of quality chunks
        quantity_bonus = min(0.1, len(selected_chunks) * 0.01)

        confidence = min(avg_score + diversity_bonus + quantity_bonus, 1.0)
        return round(confidence, 3)

    def _calculate_confidence(self, search_results: Dict[str, List[Dict[str, Any]]]) -> float:
        """Calculate confidence score based on retrieval results"""
        all_scores = []

        # Collect all scores from both collections
        for collection_results in search_results.values():
            for result in collection_results:
                all_scores.append(result.get('score', 0.0))

        if not all_scores:
            return 0.0

        # Calculate average score and normalize
        avg_score = sum(all_scores) / len(all_scores)

        # Convert to confidence (0.0 to 1.0)
        # Qdrant scores are typically between 0 and 1, with higher being better
        confidence = min(avg_score, 1.0)

        return round(confidence, 3)

    async def get_pipeline_stats(self) -> Dict[str, Any]:
        """Get statistics about the RAG pipeline"""
        if not self.initialized:
            return {"error": "Pipeline not initialized"}

        try:
            collection_stats = await self.qdrant_manager.get_collection_stats()

            return {
                "initialized": self.initialized,
                "collections": collection_stats,
                "config": {
                    "rag_top_k": self.config.RAG_TOP_K,
                    "openai_model": self.config.OPENAI_MODEL,
                    "embedding_model": self.config.OPENAI_EMBEDDING_MODEL
                }
            }

        except Exception as e:
            return {"error": str(e)}