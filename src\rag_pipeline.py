"""
RAG Pipeline for handling retrieval-augmented generation
"""
import asyncio
from typing import List, Dict, Any, Optional
from openai import Open<PERSON><PERSON>

from src.qdrant_manager import <PERSON>drant<PERSON>anager
from src.models import RAGResult
from config import Config


class RAGPipeline:
    """
    Retrieval-Augmented Generation pipeline that combines vector search with LLM generation
    """

    def __init__(self):
        self.config = Config
        self.qdrant_manager = QdrantManager()
        self.openai_client = None
        self.initialized = False

    async def initialize(self):
        """Initialize the RAG pipeline components"""
        try:
            # Initialize Qdrant manager
            await self.qdrant_manager.initialize()

            # Initialize OpenAI client
            self.openai_client = OpenAI(
                api_key=self.config.OPENAI_API_KEY
            )

            self.initialized = True
            print("RAG Pipeline initialized successfully")

        except Exception as e:
            raise Exception(f"Failed to initialize RAG Pipeline: {str(e)}")

    async def query(self, query: str, conversation_history: List[Dict[str, str]] = None) -> RAGResult:
        """
        Process a query through the RAG pipeline

        Args:
            query: User's question
            conversation_history: Previous conversation messages

        Returns:
            RAGResult with answer, sources, context chunks, and confidence
        """
        if not self.initialized:
            raise Exception("RAG Pipeline not initialized. Call initialize() first.")

        try:
            # Step 1: Retrieve relevant context from both collections
            search_results = await self.qdrant_manager.search_both_collections(
                query=query,
                limit_per_collection=self.config.RAG_TOP_K,
                score_threshold=0.7
            )

            # Step 2: Combine and format context
            context_chunks = []
            sources = []

            # Process TAX-RAG-1 results
            for result in search_results.get('tax_rag', []):
                context_chunks.append(result['text'])
                if result.get('metadata', {}).get('source'):
                    sources.append(result['metadata']['source'])

            # Process tax_documents results
            for result in search_results.get('tax_documents', []):
                context_chunks.append(result['text'])
                if result.get('metadata', {}).get('source'):
                    sources.append(result['metadata']['source'])

            # Remove duplicates while preserving order
            sources = list(dict.fromkeys(sources))

            # Step 3: Generate answer using retrieved context
            answer = await self._generate_answer(
                query=query,
                context_chunks=context_chunks,
                conversation_history=conversation_history or []
            )

            # Step 4: Calculate confidence based on retrieval scores
            confidence = self._calculate_confidence(search_results)

            return RAGResult(
                answer=answer,
                sources=sources,
                context_chunks=context_chunks,
                confidence=confidence
            )

        except Exception as e:
            # Return error response
            return RAGResult(
                answer=f"I apologize, but I encountered an error while processing your question: {str(e)}",
                sources=[],
                context_chunks=[],
                confidence=0.0
            )

    async def _generate_answer(
        self,
        query: str,
        context_chunks: List[str],
        conversation_history: List[Dict[str, str]]
    ) -> str:
        """Generate answer using OpenAI with retrieved context"""

        # Build context string
        context_text = "\n\n".join([f"Context {i+1}:\n{chunk}" for i, chunk in enumerate(context_chunks)])

        # Build conversation history string
        history_text = ""
        if conversation_history:
            history_text = "\n".join([
                f"{msg['role'].title()}: {msg['content']}"
                for msg in conversation_history[-4:]  # Last 4 messages for context
            ])

        # Create system prompt
        system_prompt = """You are a helpful AI assistant specializing in tax-related questions.
Use the provided context to answer the user's question accurately and comprehensively.

Guidelines:
- Base your answer primarily on the provided context
- If the context doesn't contain enough information, say so clearly
- Provide specific details and examples when available
- Be concise but thorough
- If asked about tax calculations, show step-by-step reasoning
- Always maintain a professional and helpful tone"""

        # Create user prompt
        user_prompt = f"""Context Information:
{context_text}

{"Previous Conversation:" + chr(10) + history_text + chr(10) if history_text else ""}
Current Question: {query}

Please provide a comprehensive answer based on the context provided."""

        try:
            # Generate response using OpenAI
            response = await asyncio.to_thread(
                self.openai_client.chat.completions.create,
                model=self.config.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=1000,
                temperature=0.3
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            return f"I apologize, but I encountered an error generating the response: {str(e)}"

    def _calculate_confidence(self, search_results: Dict[str, List[Dict[str, Any]]]) -> float:
        """Calculate confidence score based on retrieval results"""
        all_scores = []

        # Collect all scores from both collections
        for collection_results in search_results.values():
            for result in collection_results:
                all_scores.append(result.get('score', 0.0))

        if not all_scores:
            return 0.0

        # Calculate average score and normalize
        avg_score = sum(all_scores) / len(all_scores)

        # Convert to confidence (0.0 to 1.0)
        # Qdrant scores are typically between 0 and 1, with higher being better
        confidence = min(avg_score, 1.0)

        return round(confidence, 3)

    async def get_pipeline_stats(self) -> Dict[str, Any]:
        """Get statistics about the RAG pipeline"""
        if not self.initialized:
            return {"error": "Pipeline not initialized"}

        try:
            collection_stats = await self.qdrant_manager.get_collection_stats()

            return {
                "initialized": self.initialized,
                "collections": collection_stats,
                "config": {
                    "rag_top_k": self.config.RAG_TOP_K,
                    "openai_model": self.config.OPENAI_MODEL,
                    "embedding_model": self.config.OPENAI_EMBEDDING_MODEL
                }
            }

        except Exception as e:
            return {"error": str(e)}